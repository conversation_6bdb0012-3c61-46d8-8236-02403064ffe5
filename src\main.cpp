#include <iostream>
#include <memory>
#include <signal.h>
#include <thread>
#include <chrono>
#include <sstream>
#include <fstream>
#ifdef _WIN32
#include <windows.h>
#endif
#include "AudioCapture.h"
#include "SpeechRecognizer.h"
#include "VoiceActivityDetector.h"
#include "WebSocketServer.h"
#include "ConfigManager.h"
#include "TranscriptionManager.h"
#include "ModelDownloader.h"

// Components are now properly included from headers

class UltraFlexSTTApp
{
public:
    UltraFlexSTTApp() : isRunning_(false) {}

    bool Initialize()
    {
        std::cout << "UltraFlex STT C++ - Initializing..." << std::endl;

        // Load configuration
        config_ = std::make_unique<ConfigManager>();
        if (!config_->Load("config.json"))
        {
            std::cerr << "Warning: Could not load config.json, using defaults" << std::endl;
        }

        // Initialize audio capture
        audioCapture_ = std::make_unique<AudioCapture>();
        if (!audioCapture_->Initialize(16000, 512))
        {
            std::cerr << "Failed to initialize audio capture" << std::endl;
            return false;
        }

        // Initialize speech recognizer
        recognizer_ = std::make_unique<SpeechRecognizer>();
        SpeechRecognizer::Config recConfig;
        recConfig.model = static_cast<SpeechRecognizer::Model>(config_->GetInt("model", 0));
        recConfig.language = config_->GetString("language", "en");
        recConfig.threads = config_->GetInt("threads", 4);
        recConfig.beamSize = config_->GetInt("beam_size", 5);

        if (!recognizer_->Initialize(recConfig))
        {
            std::cerr << "Failed to initialize speech recognizer" << std::endl;
            return false;
        }

        // Initialize VAD with ultra-sensitive settings
        vad_ = std::make_unique<VoiceActivityDetector>();
        VoiceActivityDetector::Config vadConfig;
        vadConfig.energyThreshold = 0.000001f; // Ultra-sensitive for low-gain mics
        vadConfig.silenceThreshold = 0.000001f;
        vadConfig.silenceFrames = 15;     // Shorter silence detection
        vadConfig.speechFrames = 3;       // Very fast response
        vadConfig.smoothingFactor = 0.1f; // Light smoothing
        vad_->Initialize(vadConfig, 16000);
        vad_->EnableDynamicThreshold(true); // Enable adaptive threshold

        std::cout << "VAD initialized with ultra-sensitive settings: energyThreshold=" << vadConfig.energyThreshold << std::endl;

        // Initialize transcription manager
        transcriptionManager_ = std::make_unique<TranscriptionManager>();

        // Initialize model downloader
        modelDownloader_ = std::make_unique<ModelDownloader>();

        // Initialize WebSocket server
        wsServer_ = std::make_unique<WebSocketServer>();
        wsServer_->SetMessageHandler([this](const std::string &msg)
                                     { HandleWebSocketMessage(msg); });

        if (!wsServer_->Start(8765))
        {
            std::cerr << "Failed to start WebSocket server" << std::endl;
            return false;
        }

        // Set up callbacks
        recognizer_->SetCallback([this](const std::string &text, bool isFinal)
                                 { HandleTranscription(text, isFinal); });

        std::cout << "UltraFlex STT initialized successfully!" << std::endl;
        std::cout << "WebSocket server listening on port 8765" << std::endl;

        return true;
    }

    void Run()
    {
        isRunning_ = true;

        // Start audio capture with callback
        audioCapture_->StartCapture([this](const std::vector<float> &audio)
                                    { ProcessAudioData(audio); });

        // Keep running until interrupted
        while (isRunning_)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        Shutdown();
    }

    void Stop()
    {
        isRunning_ = false;
    }

private:
    void ProcessAudioData(const std::vector<float> &audioData)
    {
        // Process through VAD
        bool hasVoice = vad_->ProcessFrame(audioData);

        // Send audio to recognizer if voice detected or currently speaking
        if (hasVoice || vad_->IsSpeaking())
        {
            recognizer_->ProcessAudio(audioData);
        }

        // Send VAD status to UI (throttled to reduce spam)
        static auto lastVadBroadcast = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (wsServer_ && std::chrono::duration_cast<std::chrono::milliseconds>(now - lastVadBroadcast).count() > 100)
        {
            float energy = vad_->GetCurrentEnergy();
            std::string vadStatus = R"({"type":"vad","speaking":)" +
                                    std::string(vad_->IsSpeaking() ? "true" : "false") +
                                    R"(,"energy":)" + std::to_string(energy) + "}";
            wsServer_->Broadcast(vadStatus);
            lastVadBroadcast = now;
        }
    }

    void HandleTranscription(const std::string &text, bool isFinal)
    {
        if (text.empty())
            return;

        // Add to transcription history
        transcriptionManager_->AddTranscription(text, isFinal);

        // Send to UI via WebSocket
        std::string message = R"({"type":"transcription","text":")" +
                              EscapeJson(text) + R"(","final":)" +
                              (isFinal ? "true" : "false") + "}";
        wsServer_->Broadcast(message);

        // Log to console
        std::cout << (isFinal ? "[FINAL] " : "[PARTIAL] ") << text << std::endl;
    }

    void HandleWebSocketMessage(const std::string &message)
    {
        std::cout << "Received WebSocket message: " << message << std::endl;

        // Parse commands from UI
        if (message == "start")
        {
            std::cout << "Starting audio capture..." << std::endl;
            audioCapture_->StartCapture([this](const std::vector<float> &audio)
                                        { ProcessAudioData(audio); });
        }
        else if (message == "stop")
        {
            std::cout << "Stopping audio capture..." << std::endl;
            audioCapture_->StopCapture();
        }
        else if (message == "clear")
        {
            std::cout << "Clearing transcription..." << std::endl;
            transcriptionManager_->Clear();
        }
        else if (message == "get_models")
        {
            std::cout << "Sending model list..." << std::endl;
            SendModelList();
        }
        else if (message.substr(0, 15) == "download_model:")
        {
            std::string modelName = message.substr(15);
            std::cout << "Downloading model: " << modelName << std::endl;
            DownloadModel(modelName);
        }
        else if (message.substr(0, 7) == "config:")
        {
            // Handle configuration updates
            UpdateConfiguration(message.substr(7));
        }
    }

    void UpdateConfiguration(const std::string &configJson)
    {
        // Parse and update configuration
        // This would use a JSON parser in the real implementation
    }

    std::string EscapeJson(const std::string &s)
    {
        std::string result;
        for (char c : s)
        {
            switch (c)
            {
            case '"':
                result += "\\\"";
                break;
            case '\\':
                result += "\\\\";
                break;
            case '\n':
                result += "\\n";
                break;
            case '\r':
                result += "\\r";
                break;
            case '\t':
                result += "\\t";
                break;
            default:
                result += c;
            }
        }
        return result;
    }

    void Shutdown()
    {
        if (audioCapture_)
        {
            audioCapture_->StopCapture();
            audioCapture_->Shutdown();
        }

        if (recognizer_)
        {
            recognizer_->Shutdown();
        }

        if (wsServer_)
        {
            wsServer_->Stop();
        }
    }

    void SendModelList()
    {
        auto models = modelDownloader_->GetAvailableModels();

        // Check actual file existence for each model
        for (auto &[name, info] : models)
        {
            std::string modelPath;

#ifdef _WIN32
            // Get the directory where the executable is located
            char buffer[MAX_PATH];
            GetModuleFileNameA(NULL, buffer, MAX_PATH);
            std::string exePath(buffer);
            std::string exeDir = exePath.substr(0, exePath.find_last_of("\\/"));

            // Go up two directories from build/Release/ to reach project root
            modelPath = exeDir + "/../../models/ggml-" + name + ".bin";
#else
            modelPath = "../../models/ggml-" + name + ".bin";
#endif

            std::ifstream file(modelPath);
            info.isDownloaded = file.good();
            file.close();

            // Debug output
            std::cout << "Checking model " << name << " at " << modelPath << ": " << (info.isDownloaded ? "FOUND" : "NOT FOUND") << std::endl;
        }

        std::stringstream json;
        json << R"({"type":"models","models":[)";

        bool first = true;
        for (const auto &[name, info] : models)
        {
            if (!first)
                json << ",";
            json << R"({"name":")" << name << R"(",)";
            json << R"("size":)" << info.size << ",";
            json << R"("downloaded":)" << (info.isDownloaded ? "true" : "false") << "}";
            first = false;
        }

        json << "]}";
        wsServer_->Broadcast(json.str());
    }

    void DownloadModel(const std::string &modelName)
    {
        modelDownloader_->DownloadModel(modelName, [this](const std::string &model, float progress)
                                        {
                // Send progress updates
                std::stringstream json;
                json << R"({"type":"download_progress","model":")" << model;
                json << R"(","progress":)" << progress << "}";
                wsServer_->Broadcast(json.str()); }, [this](const std::string &model, bool success, const std::string &error)
                                        {
                // Send completion status
                std::stringstream json;
                json << R"({"type":"download_complete","model":")" << model;
                json << R"(","success":)" << (success ? "true" : "false");
                if (!error.empty()) {
                    json << R"(,"error":")" << EscapeJson(error) << R"(")";
                }
                json << "}";
                wsServer_->Broadcast(json.str());

                // Refresh model list
                SendModelList(); });
    }

    std::unique_ptr<ConfigManager> config_;
    std::unique_ptr<AudioCapture> audioCapture_;
    std::unique_ptr<SpeechRecognizer> recognizer_;
    std::unique_ptr<VoiceActivityDetector> vad_;
    std::unique_ptr<TranscriptionManager> transcriptionManager_;
    std::unique_ptr<WebSocketServer> wsServer_;
    std::unique_ptr<ModelDownloader> modelDownloader_;

    std::atomic<bool> isRunning_;
};

// Global app instance for signal handling
std::unique_ptr<UltraFlexSTTApp> g_app;

void SignalHandler(int signal)
{
    std::cout << "\nShutting down..." << std::endl;
    if (g_app)
    {
        g_app->Stop();
    }
}

int main(int argc, char *argv[])
{
    // Set up signal handlers
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

    // Create and run application
    g_app = std::make_unique<UltraFlexSTTApp>();

    if (!g_app->Initialize())
    {
        std::cerr << "Failed to initialize application" << std::endl;
        return 1;
    }

    std::cout << "UltraFlex STT is running. Press Ctrl+C to stop." << std::endl;
    g_app->Run();

    return 0;
}