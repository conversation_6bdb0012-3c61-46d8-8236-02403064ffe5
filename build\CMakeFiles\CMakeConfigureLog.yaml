
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - ARM64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 5/29/2025 1:05:04 AM.
      
      Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostarm64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.95
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 5/29/2025 1:05:05 AM.
      
      Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostarm64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.68
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9thyde"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9thyde"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9thyde'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_24369.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:06 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9thyde\\cmTC_24369.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_24369.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9thyde\\Debug\\".
          Creating directory "cmTC_24369.dir\\Debug\\cmTC_24369.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_24369.dir\\Debug\\cmTC_24369.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_24369.dir\\Debug\\cmTC_24369.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_24369.dir\\Debug\\\\" /Fd"cmTC_24369.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_24369.dir\\Debug\\\\" /Fd"cmTC_24369.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9thyde\\Debug\\cmTC_24369.exe" /INCREMENTAL /ILK:"cmTC_24369.dir\\Debug\\cmTC_24369.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9thyde/Debug/cmTC_24369.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-9thyde/Debug/cmTC_24369.lib" /MACHINE:X64  /machine:x64 cmTC_24369.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_24369.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9thyde\\Debug\\cmTC_24369.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_24369.dir\\Debug\\cmTC_24369.tlog\\unsuccessfulbuild".
          Touching "cmTC_24369.dir\\Debug\\cmTC_24369.tlog\\cmTC_24369.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9thyde\\cmTC_24369.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xuza9u"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xuza9u"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xuza9u'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_2fce2.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:07 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xuza9u\\cmTC_2fce2.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_2fce2.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xuza9u\\Debug\\".
          Creating directory "cmTC_2fce2.dir\\Debug\\cmTC_2fce2.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_2fce2.dir\\Debug\\cmTC_2fce2.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_2fce2.dir\\Debug\\cmTC_2fce2.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_2fce2.dir\\Debug\\\\" /Fd"cmTC_2fce2.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_2fce2.dir\\Debug\\\\" /Fd"cmTC_2fce2.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xuza9u\\Debug\\cmTC_2fce2.exe" /INCREMENTAL /ILK:"cmTC_2fce2.dir\\Debug\\cmTC_2fce2.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xuza9u/Debug/cmTC_2fce2.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xuza9u/Debug/cmTC_2fce2.lib" /MACHINE:X64  /machine:x64 cmTC_2fce2.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_2fce2.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xuza9u\\Debug\\cmTC_2fce2.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_2fce2.dir\\Debug\\cmTC_2fce2.tlog\\unsuccessfulbuild".
          Touching "cmTC_2fce2.dir\\Debug\\cmTC_2fce2.tlog\\cmTC_2fce2.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xuza9u\\cmTC_2fce2.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-81c4hl"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-81c4hl"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-81c4hl'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_67b09.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:08 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\cmTC_67b09.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_67b09.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\Debug\\".
          Creating directory "cmTC_67b09.dir\\Debug\\cmTC_67b09.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_67b09.dir\\Debug\\cmTC_67b09.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_67b09.dir\\Debug\\cmTC_67b09.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_67b09.dir\\Debug\\\\" /Fd"cmTC_67b09.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_67b09.dir\\Debug\\\\" /Fd"cmTC_67b09.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\src.c"
          src.c
        C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\cmTC_67b09.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\cmTC_67b09.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\cmTC_67b09.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-81c4hl\\cmTC_67b09.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.44
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7caf8o"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7caf8o"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7caf8o'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_7ccec.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:09 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\cmTC_7ccec.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_7ccec.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\Debug\\".
          Creating directory "cmTC_7ccec.dir\\Debug\\cmTC_7ccec.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_7ccec.dir\\Debug\\cmTC_7ccec.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_7ccec.dir\\Debug\\cmTC_7ccec.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7ccec.dir\\Debug\\\\" /Fd"cmTC_7ccec.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7ccec.dir\\Debug\\\\" /Fd"cmTC_7ccec.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\Debug\\cmTC_7ccec.exe" /INCREMENTAL /ILK:"cmTC_7ccec.dir\\Debug\\cmTC_7ccec.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7caf8o/Debug/cmTC_7ccec.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7caf8o/Debug/cmTC_7ccec.lib" /MACHINE:X64  /machine:x64 cmTC_7ccec.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\cmTC_7ccec.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\cmTC_7ccec.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\cmTC_7ccec.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7caf8o\\cmTC_7ccec.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.45
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zma4pw"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zma4pw"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zma4pw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_df370.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:10 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\cmTC_df370.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_df370.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\Debug\\".
          Creating directory "cmTC_df370.dir\\Debug\\cmTC_df370.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_df370.dir\\Debug\\cmTC_df370.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_df370.dir\\Debug\\cmTC_df370.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_df370.dir\\Debug\\\\" /Fd"cmTC_df370.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_df370.dir\\Debug\\\\" /Fd"cmTC_df370.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\Debug\\cmTC_df370.exe" /INCREMENTAL /ILK:"cmTC_df370.dir\\Debug\\cmTC_df370.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zma4pw/Debug/cmTC_df370.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zma4pw/Debug/cmTC_df370.lib" /MACHINE:X64  /machine:x64 cmTC_df370.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\cmTC_df370.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\cmTC_df370.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\cmTC_df370.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zma4pw\\cmTC_df370.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.45
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-kjwobi"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-kjwobi"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-kjwobi'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_5384c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:11 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwobi\\cmTC_5384c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_5384c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwobi\\Debug\\".
          Creating directory "cmTC_5384c.dir\\Debug\\cmTC_5384c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_5384c.dir\\Debug\\cmTC_5384c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_5384c.dir\\Debug\\cmTC_5384c.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_5384c.dir\\Debug\\\\" /Fd"cmTC_5384c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwobi\\OpenMPTryFlag.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_5384c.dir\\Debug\\\\" /Fd"cmTC_5384c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwobi\\OpenMPTryFlag.c"
          OpenMPTryFlag.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwobi\\Debug\\cmTC_5384c.exe" /INCREMENTAL /ILK:"cmTC_5384c.dir\\Debug\\cmTC_5384c.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-kjwobi/Debug/cmTC_5384c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-kjwobi/Debug/cmTC_5384c.lib" /MACHINE:X64  /machine:x64 cmTC_5384c.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_5384c.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwobi\\Debug\\cmTC_5384c.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_5384c.dir\\Debug\\cmTC_5384c.tlog\\unsuccessfulbuild".
          Touching "cmTC_5384c.dir\\Debug\\cmTC_5384c.tlog\\cmTC_5384c.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwobi\\cmTC_5384c.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m5ravl"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m5ravl"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m5ravl'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_f8ed1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:11 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m5ravl\\cmTC_f8ed1.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f8ed1.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m5ravl\\Debug\\".
          Creating directory "cmTC_f8ed1.dir\\Debug\\cmTC_f8ed1.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f8ed1.dir\\Debug\\cmTC_f8ed1.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_f8ed1.dir\\Debug\\cmTC_f8ed1.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_f8ed1.dir\\Debug\\\\" /Fd"cmTC_f8ed1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m5ravl\\OpenMPTryFlag.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_f8ed1.dir\\Debug\\\\" /Fd"cmTC_f8ed1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m5ravl\\OpenMPTryFlag.cpp"
          OpenMPTryFlag.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m5ravl\\Debug\\cmTC_f8ed1.exe" /INCREMENTAL /ILK:"cmTC_f8ed1.dir\\Debug\\cmTC_f8ed1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m5ravl/Debug/cmTC_f8ed1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-m5ravl/Debug/cmTC_f8ed1.lib" /MACHINE:X64  /machine:x64 cmTC_f8ed1.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_f8ed1.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m5ravl\\Debug\\cmTC_f8ed1.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_f8ed1.dir\\Debug\\cmTC_f8ed1.tlog\\unsuccessfulbuild".
          Touching "cmTC_f8ed1.dir\\Debug\\cmTC_f8ed1.tlog\\cmTC_f8ed1.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m5ravl\\cmTC_f8ed1.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h8ke2"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h8ke2"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h8ke2'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_268f6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:12 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h8ke2\\cmTC_268f6.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_268f6.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h8ke2\\Debug\\".
          Creating directory "cmTC_268f6.dir\\Debug\\cmTC_268f6.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_268f6.dir\\Debug\\cmTC_268f6.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_268f6.dir\\Debug\\cmTC_268f6.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_268f6.dir\\Debug\\\\" /Fd"cmTC_268f6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h8ke2\\OpenMPCheckVersion.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_268f6.dir\\Debug\\\\" /Fd"cmTC_268f6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h8ke2\\OpenMPCheckVersion.c"
          OpenMPCheckVersion.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h8ke2\\Debug\\cmTC_268f6.exe" /INCREMENTAL /ILK:"cmTC_268f6.dir\\Debug\\cmTC_268f6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h8ke2/Debug/cmTC_268f6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-0h8ke2/Debug/cmTC_268f6.lib" /MACHINE:X64  /machine:x64 cmTC_268f6.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_268f6.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h8ke2\\Debug\\cmTC_268f6.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_268f6.dir\\Debug\\cmTC_268f6.tlog\\unsuccessfulbuild".
          Touching "cmTC_268f6.dir\\Debug\\cmTC_268f6.tlog\\cmTC_268f6.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0h8ke2\\cmTC_268f6.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.67
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7yn9q1"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7yn9q1"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7yn9q1'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_47f1c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:13 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7yn9q1\\cmTC_47f1c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_47f1c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7yn9q1\\Debug\\".
          Creating directory "cmTC_47f1c.dir\\Debug\\cmTC_47f1c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_47f1c.dir\\Debug\\cmTC_47f1c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_47f1c.dir\\Debug\\cmTC_47f1c.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_47f1c.dir\\Debug\\\\" /Fd"cmTC_47f1c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7yn9q1\\OpenMPCheckVersion.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_47f1c.dir\\Debug\\\\" /Fd"cmTC_47f1c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7yn9q1\\OpenMPCheckVersion.cpp"
          OpenMPCheckVersion.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7yn9q1\\Debug\\cmTC_47f1c.exe" /INCREMENTAL /ILK:"cmTC_47f1c.dir\\Debug\\cmTC_47f1c.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7yn9q1/Debug/cmTC_47f1c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7yn9q1/Debug/cmTC_47f1c.lib" /MACHINE:X64  /machine:x64 cmTC_47f1c.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_47f1c.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7yn9q1\\Debug\\cmTC_47f1c.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_47f1c.dir\\Debug\\cmTC_47f1c.tlog\\unsuccessfulbuild".
          Touching "cmTC_47f1c.dir\\Debug\\cmTC_47f1c.tlog\\cmTC_47f1c.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7yn9q1\\cmTC_47f1c.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.64
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-inoqv5"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-inoqv5"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-inoqv5'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_31fbe.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:14 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-inoqv5\\cmTC_31fbe.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_31fbe.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-inoqv5\\Debug\\".
          Creating directory "cmTC_31fbe.dir\\Debug\\cmTC_31fbe.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_31fbe.dir\\Debug\\cmTC_31fbe.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_31fbe.dir\\Debug\\cmTC_31fbe.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_31fbe.dir\\Debug\\\\" /Fd"cmTC_31fbe.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-inoqv5\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_31fbe.dir\\Debug\\\\" /Fd"cmTC_31fbe.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-inoqv5\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-inoqv5\\Debug\\cmTC_31fbe.exe" /INCREMENTAL /ILK:"cmTC_31fbe.dir\\Debug\\cmTC_31fbe.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-inoqv5/Debug/cmTC_31fbe.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-inoqv5/Debug/cmTC_31fbe.lib" /MACHINE:X64  /machine:x64 cmTC_31fbe.dir\\Debug\\src.obj
          cmTC_31fbe.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-inoqv5\\Debug\\cmTC_31fbe.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_31fbe.dir\\Debug\\cmTC_31fbe.tlog\\unsuccessfulbuild".
          Touching "cmTC_31fbe.dir\\Debug\\cmTC_31fbe.tlog\\cmTC_31fbe.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-inoqv5\\cmTC_31fbe.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.72
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-amjsi4"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-amjsi4"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-amjsi4'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_907df.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:16 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-amjsi4\\cmTC_907df.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_907df.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-amjsi4\\Debug\\".
          Creating directory "cmTC_907df.dir\\Debug\\cmTC_907df.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_907df.dir\\Debug\\cmTC_907df.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_907df.dir\\Debug\\cmTC_907df.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_907df.dir\\Debug\\\\" /Fd"cmTC_907df.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-amjsi4\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_907df.dir\\Debug\\\\" /Fd"cmTC_907df.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-amjsi4\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-amjsi4\\Debug\\cmTC_907df.exe" /INCREMENTAL /ILK:"cmTC_907df.dir\\Debug\\cmTC_907df.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-amjsi4/Debug/cmTC_907df.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-amjsi4/Debug/cmTC_907df.lib" /MACHINE:X64  /machine:x64 cmTC_907df.dir\\Debug\\src.obj
          cmTC_907df.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-amjsi4\\Debug\\cmTC_907df.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_907df.dir\\Debug\\cmTC_907df.tlog\\unsuccessfulbuild".
          Touching "cmTC_907df.dir\\Debug\\cmTC_907df.tlog\\cmTC_907df.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-amjsi4\\cmTC_907df.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.63
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-2lu18s"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-2lu18s"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-2lu18s'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_f398d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:17 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lu18s\\cmTC_f398d.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f398d.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lu18s\\Debug\\".
          Creating directory "cmTC_f398d.dir\\Debug\\cmTC_f398d.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f398d.dir\\Debug\\cmTC_f398d.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_f398d.dir\\Debug\\cmTC_f398d.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_f398d.dir\\Debug\\\\" /Fd"cmTC_f398d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lu18s\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_f398d.dir\\Debug\\\\" /Fd"cmTC_f398d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lu18s\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lu18s\\Debug\\cmTC_f398d.exe" /INCREMENTAL /ILK:"cmTC_f398d.dir\\Debug\\cmTC_f398d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-2lu18s/Debug/cmTC_f398d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-2lu18s/Debug/cmTC_f398d.lib" /MACHINE:X64  /machine:x64 cmTC_f398d.dir\\Debug\\src.obj
          cmTC_f398d.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lu18s\\Debug\\cmTC_f398d.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_f398d.dir\\Debug\\cmTC_f398d.tlog\\unsuccessfulbuild".
          Touching "cmTC_f398d.dir\\Debug\\cmTC_f398d.tlog\\cmTC_f398d.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lu18s\\cmTC_f398d.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.65
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-16nq4n"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-16nq4n"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-16nq4n'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_1c94e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:19 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-16nq4n\\cmTC_1c94e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_1c94e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-16nq4n\\Debug\\".
          Creating directory "cmTC_1c94e.dir\\Debug\\cmTC_1c94e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_1c94e.dir\\Debug\\cmTC_1c94e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_1c94e.dir\\Debug\\cmTC_1c94e.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_1c94e.dir\\Debug\\\\" /Fd"cmTC_1c94e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-16nq4n\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_1c94e.dir\\Debug\\\\" /Fd"cmTC_1c94e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-16nq4n\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-16nq4n\\Debug\\cmTC_1c94e.exe" /INCREMENTAL /ILK:"cmTC_1c94e.dir\\Debug\\cmTC_1c94e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-16nq4n/Debug/cmTC_1c94e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-16nq4n/Debug/cmTC_1c94e.lib" /MACHINE:X64  /machine:x64 cmTC_1c94e.dir\\Debug\\src.obj
          cmTC_1c94e.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-16nq4n\\Debug\\cmTC_1c94e.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_1c94e.dir\\Debug\\cmTC_1c94e.tlog\\unsuccessfulbuild".
          Touching "cmTC_1c94e.dir\\Debug\\cmTC_1c94e.tlog\\cmTC_1c94e.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-16nq4n\\cmTC_1c94e.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.69
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i3bzbk"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i3bzbk"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i3bzbk'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_597cf.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:20 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i3bzbk\\cmTC_597cf.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_597cf.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i3bzbk\\Debug\\".
          Creating directory "cmTC_597cf.dir\\Debug\\cmTC_597cf.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_597cf.dir\\Debug\\cmTC_597cf.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_597cf.dir\\Debug\\cmTC_597cf.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_597cf.dir\\Debug\\\\" /Fd"cmTC_597cf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i3bzbk\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_597cf.dir\\Debug\\\\" /Fd"cmTC_597cf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i3bzbk\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i3bzbk\\Debug\\cmTC_597cf.exe" /INCREMENTAL /ILK:"cmTC_597cf.dir\\Debug\\cmTC_597cf.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i3bzbk/Debug/cmTC_597cf.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i3bzbk/Debug/cmTC_597cf.lib" /MACHINE:X64  /machine:x64 cmTC_597cf.dir\\Debug\\src.obj
          cmTC_597cf.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i3bzbk\\Debug\\cmTC_597cf.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_597cf.dir\\Debug\\cmTC_597cf.tlog\\unsuccessfulbuild".
          Touching "cmTC_597cf.dir\\Debug\\cmTC_597cf.tlog\\cmTC_597cf.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i3bzbk\\cmTC_597cf.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-3hxrut"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-3hxrut"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-3hxrut'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_fefa8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:21 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3hxrut\\cmTC_fefa8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_fefa8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3hxrut\\Debug\\".
          Creating directory "cmTC_fefa8.dir\\Debug\\cmTC_fefa8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_fefa8.dir\\Debug\\cmTC_fefa8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_fefa8.dir\\Debug\\cmTC_fefa8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_fefa8.dir\\Debug\\\\" /Fd"cmTC_fefa8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3hxrut\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_fefa8.dir\\Debug\\\\" /Fd"cmTC_fefa8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3hxrut\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3hxrut\\Debug\\cmTC_fefa8.exe" /INCREMENTAL /ILK:"cmTC_fefa8.dir\\Debug\\cmTC_fefa8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-3hxrut/Debug/cmTC_fefa8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-3hxrut/Debug/cmTC_fefa8.lib" /MACHINE:X64  /machine:x64 cmTC_fefa8.dir\\Debug\\src.obj
          cmTC_fefa8.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3hxrut\\Debug\\cmTC_fefa8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_fefa8.dir\\Debug\\cmTC_fefa8.tlog\\unsuccessfulbuild".
          Touching "cmTC_fefa8.dir\\Debug\\cmTC_fefa8.tlog\\cmTC_fefa8.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3hxrut\\cmTC_fefa8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.64
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-fr2vli"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-fr2vli"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-fr2vli'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_8d227.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:23 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fr2vli\\cmTC_8d227.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_8d227.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fr2vli\\Debug\\".
          Creating directory "cmTC_8d227.dir\\Debug\\cmTC_8d227.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_8d227.dir\\Debug\\cmTC_8d227.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_8d227.dir\\Debug\\cmTC_8d227.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_8d227.dir\\Debug\\\\" /Fd"cmTC_8d227.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fr2vli\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_8d227.dir\\Debug\\\\" /Fd"cmTC_8d227.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fr2vli\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fr2vli\\Debug\\cmTC_8d227.exe" /INCREMENTAL /ILK:"cmTC_8d227.dir\\Debug\\cmTC_8d227.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-fr2vli/Debug/cmTC_8d227.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-fr2vli/Debug/cmTC_8d227.lib" /MACHINE:X64  /machine:x64 cmTC_8d227.dir\\Debug\\src.obj
          cmTC_8d227.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fr2vli\\Debug\\cmTC_8d227.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_8d227.dir\\Debug\\cmTC_8d227.tlog\\unsuccessfulbuild".
          Touching "cmTC_8d227.dir\\Debug\\cmTC_8d227.tlog\\cmTC_8d227.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fr2vli\\cmTC_8d227.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.69
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-tfu0xi"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-tfu0xi"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-tfu0xi'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_7bb3c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:24 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tfu0xi\\cmTC_7bb3c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_7bb3c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tfu0xi\\Debug\\".
          Creating directory "cmTC_7bb3c.dir\\Debug\\cmTC_7bb3c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_7bb3c.dir\\Debug\\cmTC_7bb3c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_7bb3c.dir\\Debug\\cmTC_7bb3c.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_7bb3c.dir\\Debug\\\\" /Fd"cmTC_7bb3c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tfu0xi\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_7bb3c.dir\\Debug\\\\" /Fd"cmTC_7bb3c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tfu0xi\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tfu0xi\\Debug\\cmTC_7bb3c.exe" /INCREMENTAL /ILK:"cmTC_7bb3c.dir\\Debug\\cmTC_7bb3c.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-tfu0xi/Debug/cmTC_7bb3c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-tfu0xi/Debug/cmTC_7bb3c.lib" /MACHINE:X64  /machine:x64 cmTC_7bb3c.dir\\Debug\\src.obj
          cmTC_7bb3c.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tfu0xi\\Debug\\cmTC_7bb3c.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_7bb3c.dir\\Debug\\cmTC_7bb3c.tlog\\unsuccessfulbuild".
          Touching "cmTC_7bb3c.dir\\Debug\\cmTC_7bb3c.tlog\\cmTC_7bb3c.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tfu0xi\\cmTC_7bb3c.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.61
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "external/portaudio/cmake/modules/FindRegex.cmake:23 (check_include_file)"
      - "external/portaudio/cmake/modules/FindJACK.cmake:38 (find_package)"
      - "external/portaudio/CMakeLists.txt:138 (find_package)"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xitag7"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xitag7"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: ";C:/AI/UltraFlexSTT_CPP/external/portaudio/cmake/modules"
    buildResult:
      variable: "REGEX_H"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-xitag7'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_723eb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:05:26 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\cmTC_723eb.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_723eb.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\Debug\\".
          Creating directory "cmTC_723eb.dir\\Debug\\cmTC_723eb.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_723eb.dir\\Debug\\cmTC_723eb.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_723eb.dir\\Debug\\cmTC_723eb.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_723eb.dir\\Debug\\\\" /Fd"cmTC_723eb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\CheckIncludeFile.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_723eb.dir\\Debug\\\\" /Fd"cmTC_723eb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\CheckIncludeFile.c(1,10): error C1083: Cannot open include file: 'regex.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\cmTC_723eb.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\cmTC_723eb.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\cmTC_723eb.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\CheckIncludeFile.c(1,10): error C1083: Cannot open include file: 'regex.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xitag7\\cmTC_723eb.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.37
        
      exitCode: 1
...
