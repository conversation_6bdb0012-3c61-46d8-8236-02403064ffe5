
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - ARM64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 5/29/2025 1:51:12 AM.
      
      Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostarm64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.81
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 5/29/2025 1:51:13 AM.
      
      Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostarm64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.71
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/3.31.0-rc2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vcsyzw"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vcsyzw"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vcsyzw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_7465c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:14 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vcsyzw\\cmTC_7465c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_7465c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vcsyzw\\Debug\\".
          Creating directory "cmTC_7465c.dir\\Debug\\cmTC_7465c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_7465c.dir\\Debug\\cmTC_7465c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_7465c.dir\\Debug\\cmTC_7465c.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7465c.dir\\Debug\\\\" /Fd"cmTC_7465c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7465c.dir\\Debug\\\\" /Fd"cmTC_7465c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vcsyzw\\Debug\\cmTC_7465c.exe" /INCREMENTAL /ILK:"cmTC_7465c.dir\\Debug\\cmTC_7465c.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vcsyzw/Debug/cmTC_7465c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-vcsyzw/Debug/cmTC_7465c.lib" /MACHINE:X64  /machine:x64 cmTC_7465c.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_7465c.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vcsyzw\\Debug\\cmTC_7465c.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_7465c.dir\\Debug\\cmTC_7465c.tlog\\unsuccessfulbuild".
          Touching "cmTC_7465c.dir\\Debug\\cmTC_7465c.tlog\\cmTC_7465c.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-vcsyzw\\cmTC_7465c.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.73
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6q0bcc"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6q0bcc"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6q0bcc'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_5e5f3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:15 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6q0bcc\\cmTC_5e5f3.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_5e5f3.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6q0bcc\\Debug\\".
          Creating directory "cmTC_5e5f3.dir\\Debug\\cmTC_5e5f3.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_5e5f3.dir\\Debug\\cmTC_5e5f3.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_5e5f3.dir\\Debug\\cmTC_5e5f3.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_5e5f3.dir\\Debug\\\\" /Fd"cmTC_5e5f3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_5e5f3.dir\\Debug\\\\" /Fd"cmTC_5e5f3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6q0bcc\\Debug\\cmTC_5e5f3.exe" /INCREMENTAL /ILK:"cmTC_5e5f3.dir\\Debug\\cmTC_5e5f3.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6q0bcc/Debug/cmTC_5e5f3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6q0bcc/Debug/cmTC_5e5f3.lib" /MACHINE:X64  /machine:x64 cmTC_5e5f3.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_5e5f3.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6q0bcc\\Debug\\cmTC_5e5f3.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_5e5f3.dir\\Debug\\cmTC_5e5f3.tlog\\unsuccessfulbuild".
          Touching "cmTC_5e5f3.dir\\Debug\\cmTC_5e5f3.tlog\\cmTC_5e5f3.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6q0bcc\\cmTC_5e5f3.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.69
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7c0udl"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7c0udl"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-7c0udl'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_5e380.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:17 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\cmTC_5e380.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_5e380.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\Debug\\".
          Creating directory "cmTC_5e380.dir\\Debug\\cmTC_5e380.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_5e380.dir\\Debug\\cmTC_5e380.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_5e380.dir\\Debug\\cmTC_5e380.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5e380.dir\\Debug\\\\" /Fd"cmTC_5e380.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5e380.dir\\Debug\\\\" /Fd"cmTC_5e380.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\src.c"
          src.c
        C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\cmTC_5e380.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\cmTC_5e380.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\cmTC_5e380.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7c0udl\\cmTC_5e380.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.57
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-sw5j6t"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-sw5j6t"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-sw5j6t'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_55541.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:17 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\cmTC_55541.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_55541.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\Debug\\".
          Creating directory "cmTC_55541.dir\\Debug\\cmTC_55541.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_55541.dir\\Debug\\cmTC_55541.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_55541.dir\\Debug\\cmTC_55541.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_55541.dir\\Debug\\\\" /Fd"cmTC_55541.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_55541.dir\\Debug\\\\" /Fd"cmTC_55541.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\Debug\\cmTC_55541.exe" /INCREMENTAL /ILK:"cmTC_55541.dir\\Debug\\cmTC_55541.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-sw5j6t/Debug/cmTC_55541.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-sw5j6t/Debug/cmTC_55541.lib" /MACHINE:X64  /machine:x64 cmTC_55541.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\cmTC_55541.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\cmTC_55541.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\cmTC_55541.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sw5j6t\\cmTC_55541.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.52
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:16 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ulnnub"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ulnnub"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ulnnub'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_fbb08.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:18 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\cmTC_fbb08.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_fbb08.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\Debug\\".
          Creating directory "cmTC_fbb08.dir\\Debug\\cmTC_fbb08.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_fbb08.dir\\Debug\\cmTC_fbb08.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_fbb08.dir\\Debug\\cmTC_fbb08.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fbb08.dir\\Debug\\\\" /Fd"cmTC_fbb08.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fbb08.dir\\Debug\\\\" /Fd"cmTC_fbb08.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\Debug\\cmTC_fbb08.exe" /INCREMENTAL /ILK:"cmTC_fbb08.dir\\Debug\\cmTC_fbb08.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ulnnub/Debug/cmTC_fbb08.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-ulnnub/Debug/cmTC_fbb08.lib" /MACHINE:X64  /machine:x64 cmTC_fbb08.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\cmTC_fbb08.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\cmTC_fbb08.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\cmTC_fbb08.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ulnnub\\cmTC_fbb08.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.53
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4anzo0"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4anzo0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4anzo0'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_c17eb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:19 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4anzo0\\cmTC_c17eb.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c17eb.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4anzo0\\Debug\\".
          Creating directory "cmTC_c17eb.dir\\Debug\\cmTC_c17eb.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c17eb.dir\\Debug\\cmTC_c17eb.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c17eb.dir\\Debug\\cmTC_c17eb.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_c17eb.dir\\Debug\\\\" /Fd"cmTC_c17eb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4anzo0\\OpenMPTryFlag.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_c17eb.dir\\Debug\\\\" /Fd"cmTC_c17eb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4anzo0\\OpenMPTryFlag.c"
          OpenMPTryFlag.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4anzo0\\Debug\\cmTC_c17eb.exe" /INCREMENTAL /ILK:"cmTC_c17eb.dir\\Debug\\cmTC_c17eb.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4anzo0/Debug/cmTC_c17eb.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-4anzo0/Debug/cmTC_c17eb.lib" /MACHINE:X64  /machine:x64 cmTC_c17eb.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_c17eb.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4anzo0\\Debug\\cmTC_c17eb.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c17eb.dir\\Debug\\cmTC_c17eb.tlog\\unsuccessfulbuild".
          Touching "cmTC_c17eb.dir\\Debug\\cmTC_c17eb.tlog\\cmTC_c17eb.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4anzo0\\cmTC_c17eb.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.79
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hjflc6"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hjflc6"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hjflc6'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_74333.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:20 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hjflc6\\cmTC_74333.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_74333.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hjflc6\\Debug\\".
          Creating directory "cmTC_74333.dir\\Debug\\cmTC_74333.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_74333.dir\\Debug\\cmTC_74333.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_74333.dir\\Debug\\cmTC_74333.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_74333.dir\\Debug\\\\" /Fd"cmTC_74333.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hjflc6\\OpenMPTryFlag.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_74333.dir\\Debug\\\\" /Fd"cmTC_74333.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hjflc6\\OpenMPTryFlag.cpp"
          OpenMPTryFlag.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hjflc6\\Debug\\cmTC_74333.exe" /INCREMENTAL /ILK:"cmTC_74333.dir\\Debug\\cmTC_74333.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hjflc6/Debug/cmTC_74333.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-hjflc6/Debug/cmTC_74333.lib" /MACHINE:X64  /machine:x64 cmTC_74333.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_74333.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hjflc6\\Debug\\cmTC_74333.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_74333.dir\\Debug\\cmTC_74333.tlog\\unsuccessfulbuild".
          Touching "cmTC_74333.dir\\Debug\\cmTC_74333.tlog\\cmTC_74333.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hjflc6\\cmTC_74333.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.89
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pfqtd9"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pfqtd9"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pfqtd9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_e3e7e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:21 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfqtd9\\cmTC_e3e7e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e3e7e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfqtd9\\Debug\\".
          Creating directory "cmTC_e3e7e.dir\\Debug\\cmTC_e3e7e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e3e7e.dir\\Debug\\cmTC_e3e7e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e3e7e.dir\\Debug\\cmTC_e3e7e.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_e3e7e.dir\\Debug\\\\" /Fd"cmTC_e3e7e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfqtd9\\OpenMPCheckVersion.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c11 /Fo"cmTC_e3e7e.dir\\Debug\\\\" /Fd"cmTC_e3e7e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfqtd9\\OpenMPCheckVersion.c"
          OpenMPCheckVersion.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfqtd9\\Debug\\cmTC_e3e7e.exe" /INCREMENTAL /ILK:"cmTC_e3e7e.dir\\Debug\\cmTC_e3e7e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pfqtd9/Debug/cmTC_e3e7e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-pfqtd9/Debug/cmTC_e3e7e.lib" /MACHINE:X64  /machine:x64 cmTC_e3e7e.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_e3e7e.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfqtd9\\Debug\\cmTC_e3e7e.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e3e7e.dir\\Debug\\cmTC_e3e7e.tlog\\unsuccessfulbuild".
          Touching "cmTC_e3e7e.dir\\Debug\\cmTC_e3e7e.tlog\\cmTC_e3e7e.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfqtd9\\cmTC_e3e7e.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:02.01
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:57 (find_package)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5ctis8"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5ctis8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5ctis8'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_b80fd.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:24 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5ctis8\\cmTC_b80fd.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b80fd.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5ctis8\\Debug\\".
          Creating directory "cmTC_b80fd.dir\\Debug\\cmTC_b80fd.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b80fd.dir\\Debug\\cmTC_b80fd.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_b80fd.dir\\Debug\\cmTC_b80fd.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_b80fd.dir\\Debug\\\\" /Fd"cmTC_b80fd.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5ctis8\\OpenMPCheckVersion.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_b80fd.dir\\Debug\\\\" /Fd"cmTC_b80fd.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5ctis8\\OpenMPCheckVersion.cpp"
          OpenMPCheckVersion.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5ctis8\\Debug\\cmTC_b80fd.exe" /INCREMENTAL /ILK:"cmTC_b80fd.dir\\Debug\\cmTC_b80fd.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5ctis8/Debug/cmTC_b80fd.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5ctis8/Debug/cmTC_b80fd.lib" /MACHINE:X64  /machine:x64 cmTC_b80fd.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_b80fd.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5ctis8\\Debug\\cmTC_b80fd.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_b80fd.dir\\Debug\\cmTC_b80fd.tlog\\unsuccessfulbuild".
          Touching "cmTC_b80fd.dir\\Debug\\cmTC_b80fd.tlog\\cmTC_b80fd.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5ctis8\\cmTC_b80fd.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.67
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-85oo69"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-85oo69"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-85oo69'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_2a048.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:26 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85oo69\\cmTC_2a048.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_2a048.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85oo69\\Debug\\".
          Creating directory "cmTC_2a048.dir\\Debug\\cmTC_2a048.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_2a048.dir\\Debug\\cmTC_2a048.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_2a048.dir\\Debug\\cmTC_2a048.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_2a048.dir\\Debug\\\\" /Fd"cmTC_2a048.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85oo69\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_2a048.dir\\Debug\\\\" /Fd"cmTC_2a048.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85oo69\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85oo69\\Debug\\cmTC_2a048.exe" /INCREMENTAL /ILK:"cmTC_2a048.dir\\Debug\\cmTC_2a048.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-85oo69/Debug/cmTC_2a048.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-85oo69/Debug/cmTC_2a048.lib" /MACHINE:X64  /machine:x64 cmTC_2a048.dir\\Debug\\src.obj
          cmTC_2a048.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85oo69\\Debug\\cmTC_2a048.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_2a048.dir\\Debug\\cmTC_2a048.tlog\\unsuccessfulbuild".
          Touching "cmTC_2a048.dir\\Debug\\cmTC_2a048.tlog\\cmTC_2a048.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85oo69\\cmTC_2a048.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.82
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-g0if10"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-g0if10"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-g0if10'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_40537.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:29 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g0if10\\cmTC_40537.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_40537.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g0if10\\Debug\\".
          Creating directory "cmTC_40537.dir\\Debug\\cmTC_40537.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_40537.dir\\Debug\\cmTC_40537.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_40537.dir\\Debug\\cmTC_40537.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_40537.dir\\Debug\\\\" /Fd"cmTC_40537.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g0if10\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_40537.dir\\Debug\\\\" /Fd"cmTC_40537.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g0if10\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g0if10\\Debug\\cmTC_40537.exe" /INCREMENTAL /ILK:"cmTC_40537.dir\\Debug\\cmTC_40537.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-g0if10/Debug/cmTC_40537.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-g0if10/Debug/cmTC_40537.lib" /MACHINE:X64  /machine:x64 cmTC_40537.dir\\Debug\\src.obj
          cmTC_40537.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g0if10\\Debug\\cmTC_40537.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_40537.dir\\Debug\\cmTC_40537.tlog\\unsuccessfulbuild".
          Touching "cmTC_40537.dir\\Debug\\cmTC_40537.tlog\\cmTC_40537.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g0if10\\cmTC_40537.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.82
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-74t47b"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-74t47b"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-74t47b'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_d5f58.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:33 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-74t47b\\cmTC_d5f58.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d5f58.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-74t47b\\Debug\\".
          Creating directory "cmTC_d5f58.dir\\Debug\\cmTC_d5f58.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d5f58.dir\\Debug\\cmTC_d5f58.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_d5f58.dir\\Debug\\cmTC_d5f58.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_d5f58.dir\\Debug\\\\" /Fd"cmTC_d5f58.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-74t47b\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_d5f58.dir\\Debug\\\\" /Fd"cmTC_d5f58.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-74t47b\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-74t47b\\Debug\\cmTC_d5f58.exe" /INCREMENTAL /ILK:"cmTC_d5f58.dir\\Debug\\cmTC_d5f58.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-74t47b/Debug/cmTC_d5f58.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-74t47b/Debug/cmTC_d5f58.lib" /MACHINE:X64  /machine:x64 cmTC_d5f58.dir\\Debug\\src.obj
          cmTC_d5f58.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-74t47b\\Debug\\cmTC_d5f58.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_d5f58.dir\\Debug\\cmTC_d5f58.tlog\\unsuccessfulbuild".
          Touching "cmTC_d5f58.dir\\Debug\\cmTC_d5f58.tlog\\cmTC_d5f58.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-74t47b\\cmTC_d5f58.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.53
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5270gw"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5270gw"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5270gw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_5f8a4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:35 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5270gw\\cmTC_5f8a4.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_5f8a4.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5270gw\\Debug\\".
          Creating directory "cmTC_5f8a4.dir\\Debug\\cmTC_5f8a4.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_5f8a4.dir\\Debug\\cmTC_5f8a4.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_5f8a4.dir\\Debug\\cmTC_5f8a4.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_5f8a4.dir\\Debug\\\\" /Fd"cmTC_5f8a4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5270gw\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_5f8a4.dir\\Debug\\\\" /Fd"cmTC_5f8a4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5270gw\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5270gw\\Debug\\cmTC_5f8a4.exe" /INCREMENTAL /ILK:"cmTC_5f8a4.dir\\Debug\\cmTC_5f8a4.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5270gw/Debug/cmTC_5f8a4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-5270gw/Debug/cmTC_5f8a4.lib" /MACHINE:X64  /machine:x64 cmTC_5f8a4.dir\\Debug\\src.obj
          cmTC_5f8a4.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5270gw\\Debug\\cmTC_5f8a4.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_5f8a4.dir\\Debug\\cmTC_5f8a4.tlog\\unsuccessfulbuild".
          Touching "cmTC_5f8a4.dir\\Debug\\cmTC_5f8a4.tlog\\cmTC_5f8a4.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5270gw\\cmTC_5f8a4.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.76
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zpbl6g"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zpbl6g"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zpbl6g'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_7e028.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:38 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zpbl6g\\cmTC_7e028.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_7e028.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zpbl6g\\Debug\\".
          Creating directory "cmTC_7e028.dir\\Debug\\cmTC_7e028.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_7e028.dir\\Debug\\cmTC_7e028.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_7e028.dir\\Debug\\cmTC_7e028.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_7e028.dir\\Debug\\\\" /Fd"cmTC_7e028.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zpbl6g\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_7e028.dir\\Debug\\\\" /Fd"cmTC_7e028.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zpbl6g\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zpbl6g\\Debug\\cmTC_7e028.exe" /INCREMENTAL /ILK:"cmTC_7e028.dir\\Debug\\cmTC_7e028.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zpbl6g/Debug/cmTC_7e028.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-zpbl6g/Debug/cmTC_7e028.lib" /MACHINE:X64  /machine:x64 cmTC_7e028.dir\\Debug\\src.obj
          cmTC_7e028.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zpbl6g\\Debug\\cmTC_7e028.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_7e028.dir\\Debug\\cmTC_7e028.tlog\\unsuccessfulbuild".
          Touching "cmTC_7e028.dir\\Debug\\cmTC_7e028.tlog\\cmTC_7e028.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zpbl6g\\cmTC_7e028.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:02.67
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i4e1uv"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i4e1uv"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i4e1uv'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_3695d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:41 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i4e1uv\\cmTC_3695d.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_3695d.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i4e1uv\\Debug\\".
          Creating directory "cmTC_3695d.dir\\Debug\\cmTC_3695d.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_3695d.dir\\Debug\\cmTC_3695d.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_3695d.dir\\Debug\\cmTC_3695d.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_3695d.dir\\Debug\\\\" /Fd"cmTC_3695d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i4e1uv\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_3695d.dir\\Debug\\\\" /Fd"cmTC_3695d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i4e1uv\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i4e1uv\\Debug\\cmTC_3695d.exe" /INCREMENTAL /ILK:"cmTC_3695d.dir\\Debug\\cmTC_3695d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i4e1uv/Debug/cmTC_3695d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-i4e1uv/Debug/cmTC_3695d.lib" /MACHINE:X64  /machine:x64 cmTC_3695d.dir\\Debug\\src.obj
          cmTC_3695d.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i4e1uv\\Debug\\cmTC_3695d.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_3695d.dir\\Debug\\cmTC_3695d.tlog\\unsuccessfulbuild".
          Touching "cmTC_3695d.dir\\Debug\\cmTC_3695d.tlog\\cmTC_3695d.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-i4e1uv\\cmTC_3695d.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:02.42
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_1"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-isjqqj"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-isjqqj"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-isjqqj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_e21c8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:44 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-isjqqj\\cmTC_e21c8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e21c8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-isjqqj\\Debug\\".
          Creating directory "cmTC_e21c8.dir\\Debug\\cmTC_e21c8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e21c8.dir\\Debug\\cmTC_e21c8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e21c8.dir\\Debug\\cmTC_e21c8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_e21c8.dir\\Debug\\\\" /Fd"cmTC_e21c8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-isjqqj\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_e21c8.dir\\Debug\\\\" /Fd"cmTC_e21c8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-isjqqj\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-isjqqj\\Debug\\cmTC_e21c8.exe" /INCREMENTAL /ILK:"cmTC_e21c8.dir\\Debug\\cmTC_e21c8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-isjqqj/Debug/cmTC_e21c8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-isjqqj/Debug/cmTC_e21c8.lib" /MACHINE:X64  /machine:x64 cmTC_e21c8.dir\\Debug\\src.obj
          cmTC_e21c8.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-isjqqj\\Debug\\cmTC_e21c8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e21c8.dir\\Debug\\cmTC_e21c8.tlog\\unsuccessfulbuild".
          Touching "cmTC_e21c8.dir\\Debug\\cmTC_e21c8.tlog\\cmTC_e21c8.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-isjqqj\\cmTC_e21c8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:04.20
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceRuns.cmake:99 (try_run)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "external/whisper.cpp/ggml/src/ggml-cpu/CMakeLists.txt:182 (include)"
      - "external/whisper.cpp/ggml/src/CMakeLists.txt:302 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_2"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6g1zhz"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6g1zhz"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/AI/UltraFlexSTT_CPP/external/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6g1zhz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_71f98.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:49 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6g1zhz\\cmTC_71f98.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_71f98.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6g1zhz\\Debug\\".
          Creating directory "cmTC_71f98.dir\\Debug\\cmTC_71f98.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_71f98.dir\\Debug\\cmTC_71f98.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_71f98.dir\\Debug\\cmTC_71f98.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_71f98.dir\\Debug\\\\" /Fd"cmTC_71f98.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6g1zhz\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c11 /Fo"cmTC_71f98.dir\\Debug\\\\" /Fd"cmTC_71f98.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6g1zhz\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6g1zhz\\Debug\\cmTC_71f98.exe" /INCREMENTAL /ILK:"cmTC_71f98.dir\\Debug\\cmTC_71f98.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6g1zhz/Debug/cmTC_71f98.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-6g1zhz/Debug/cmTC_71f98.lib" /MACHINE:X64  /machine:x64 cmTC_71f98.dir\\Debug\\src.obj
          cmTC_71f98.vcxproj -> C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6g1zhz\\Debug\\cmTC_71f98.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_71f98.dir\\Debug\\cmTC_71f98.tlog\\unsuccessfulbuild".
          Touching "cmTC_71f98.dir\\Debug\\cmTC_71f98.tlog\\cmTC_71f98.lastbuildstate".
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6g1zhz\\cmTC_71f98.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:03.03
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "external/portaudio/cmake/modules/FindRegex.cmake:23 (check_include_file)"
      - "external/portaudio/cmake/modules/FindJACK.cmake:38 (find_package)"
      - "external/portaudio/CMakeLists.txt:138 (find_package)"
    directories:
      source: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bqo8ze"
      binary: "C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bqo8ze"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: ";C:/AI/UltraFlexSTT_CPP/external/portaudio/cmake/modules"
    buildResult:
      variable: "REGEX_H"
      cached: true
      stdout: |
        Change Dir: 'C:/AI/UltraFlexSTT_CPP/build/CMakeFiles/CMakeScratch/TryCompile-bqo8ze'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_2a2bc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 5/29/2025 1:51:55 AM.
        
        Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\cmTC_2a2bc.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_2a2bc.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\Debug\\".
          Creating directory "cmTC_2a2bc.dir\\Debug\\cmTC_2a2bc.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_2a2bc.dir\\Debug\\cmTC_2a2bc.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_2a2bc.dir\\Debug\\cmTC_2a2bc.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2a2bc.dir\\Debug\\\\" /Fd"cmTC_2a2bc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\CheckIncludeFile.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2a2bc.dir\\Debug\\\\" /Fd"cmTC_2a2bc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\CheckIncludeFile.c(1,10): error C1083: Cannot open include file: 'regex.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\cmTC_2a2bc.vcxproj]
        Done Building Project "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\cmTC_2a2bc.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\cmTC_2a2bc.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\CheckIncludeFile.c(1,10): error C1083: Cannot open include file: 'regex.h': No such file or directory [C:\\AI\\UltraFlexSTT_CPP\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bqo8ze\\cmTC_2a2bc.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:01.19
        
      exitCode: 1
...
