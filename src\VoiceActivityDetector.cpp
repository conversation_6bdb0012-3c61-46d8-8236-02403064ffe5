#include "VoiceActivityDetector.h"
#include <cmath>
#include <algorithm>
#include <numeric>
#include <iostream>

VoiceActivityDetector::VoiceActivityDetector()
    : sampleRate_(16000), isSpeaking_(false), speechFrameCount_(0),
      silenceFrameCount_(0), currentEnergy_(0.0f), smoothedEnergy_(0.0f),
      noiseFloor_(0.0f), useDynamicThreshold_(false)
{
}

VoiceActivityDetector::~VoiceActivityDetector() = default;

void VoiceActivityDetector::Initialize(const Config &config, int sampleRate)
{
    config_ = config;
    sampleRate_ = sampleRate;

    // Reset state
    isSpeaking_ = false;
    speechFrameCount_ = 0;
    silenceFrameCount_ = 0;
    currentEnergy_ = 0.0f;
    smoothedEnergy_ = 0.0f;
    noiseFloor_ = config.energyThreshold;
    energyHistory_.clear();

    // Ensure smoothing factor is in valid range
    if (config_.smoothingFactor <= 0.0f || config_.smoothingFactor >= 1.0f)
    {
        config_.smoothingFactor = 0.1f;
    }
}

bool VoiceActivityDetector::ProcessFrame(const std::vector<float> &audioData)
{
    if (audioData.empty())
    {
        return false;
    }

    // Calculate frame energy
    float energy = CalculateEnergy(audioData);
    currentEnergy_ = energy;

    // Update smoothed energy
    UpdateSmoothEnergy(energy);

    // Update energy history for dynamic threshold
    energyHistory_.push_back(energy);
    if (energyHistory_.size() > historySize_)
    {
        energyHistory_.pop_front();
    }

    // Determine threshold - make it much more sensitive
    float threshold = config_.energyThreshold;
    if (useDynamicThreshold_ && !energyHistory_.empty())
    {
        // Use 1.2x noise floor (much more sensitive)
        threshold = std::max(threshold, noiseFloor_ * 1.2f);
    }

    // Make threshold even more sensitive for low-gain microphones
    threshold = std::min(threshold, 0.00001f); // Cap at very low threshold

    // Check if this frame contains speech
    // Use OR instead of AND for more sensitivity
    bool frameSpeech = (currentEnergy_ > threshold) || (smoothedEnergy_ > threshold);

    // Debug speech detection
    static int speechDebugCounter = 0;
    if (speechDebugCounter++ % 200 == 0)
    { // Log every 200 frames
        std::cout << "VAD Speech Check: energy=" << currentEnergy_ << ", threshold=" << threshold
                  << ", frameSpeech=" << frameSpeech << ", isSpeaking=" << isSpeaking_ << std::endl;
    }

    if (frameSpeech)
    {
        speechFrameCount_++;
        silenceFrameCount_ = 0;

        // Start speaking after enough consecutive speech frames
        if (!isSpeaking_ && speechFrameCount_ >= config_.speechFrames)
        {
            isSpeaking_ = true;
            std::cout << "VAD: Speech detected (threshold=" << threshold << ", energy=" << currentEnergy_ << ")" << std::endl;
        }
    }
    else
    {
        silenceFrameCount_++;
        // Don't decrease speech frames - require full silence period

        // Stop speaking after enough silence frames
        if (isSpeaking_ && silenceFrameCount_ >= config_.silenceFrames)
        {
            isSpeaking_ = false;
            speechFrameCount_ = 0;
            std::cout << "VAD: Speech ended (silence frames=" << silenceFrameCount_ << ")" << std::endl;
        }
    }

    return frameSpeech;
}

void VoiceActivityDetector::UpdateNoiseFloor()
{
    if (energyHistory_.empty())
    {
        return;
    }

    // Calculate noise floor as the 20th percentile of energy history
    std::vector<float> sorted(energyHistory_.begin(), energyHistory_.end());
    std::sort(sorted.begin(), sorted.end());

    size_t index = sorted.size() / 5; // 20th percentile
    noiseFloor_ = sorted[index];
}

float VoiceActivityDetector::CalculateEnergy(const std::vector<float> &audioData)
{
    if (audioData.empty())
        return 0.0f;

    // Calculate RMS energy first (don't filter out low signals)
    float sum = 0.0f;
    for (float sample : audioData)
    {
        sum += sample * sample;
    }
    float rms = std::sqrt(sum / audioData.size());

    // Calculate max sample for debugging
    float maxSample = 0.0f;
    for (float sample : audioData)
    {
        maxSample = std::max(maxSample, std::abs(sample));
    }

    // Debug: Log actual audio levels to understand what we're getting
    static int debugCounter = 0;
    if (debugCounter++ % 100 == 0)
    { // Log every 100 frames
        std::cout << "VAD Debug: samples=" << audioData.size() << ", max=" << maxSample << ", rms=" << rms << std::endl;
    }

    // Also calculate zero crossing rate (helps distinguish speech from noise)
    int zeroCrossings = 0;
    for (size_t i = 1; i < audioData.size(); ++i)
    {
        if ((audioData[i - 1] >= 0 && audioData[i] < 0) ||
            (audioData[i - 1] < 0 && audioData[i] >= 0))
        {
            zeroCrossings++;
        }
    }
    float zcr = static_cast<float>(zeroCrossings) / audioData.size();

    // Combine RMS and ZCR for better speech detection
    // Speech typically has moderate ZCR (0.1-0.5) and higher energy
    float speechScore = rms;
    if (zcr < 0.05f || zcr > 0.6f)
    {
        // Very low or very high ZCR indicates non-speech
        speechScore *= 0.5f;
    }

    // Debug output for first few frames
    static int debugCount = 0;
    if (debugCount < 10)
    {
        std::cout << "VAD Debug: samples=" << audioData.size()
                  << ", max=" << maxSample
                  << ", rms=" << rms
                  << ", zcr=" << zcr
                  << ", score=" << speechScore << std::endl;
        debugCount++;
    }

    return speechScore;
}

void VoiceActivityDetector::UpdateSmoothEnergy(float energy)
{
    smoothedEnergy_ = (1.0f - config_.smoothingFactor) * smoothedEnergy_ +
                      config_.smoothingFactor * energy;
}