#pragma once

#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
// Forward declaration instead of include
struct whisper_context;

class SpeechRecognizer
{
public:
    enum class Model
    {
        TINY,
        BASE,
        SMALL,
        MEDIUM,
        LARGE
    };

    struct Config
    {
        Model model = Model::TINY;
        std::string language = "en";
        bool translate = false;
        int threads = 4;
        float vadThreshold = 0.6f;
        float entropyThreshold = 2.4f;
        float logprobThreshold = -1.0f;
        int beamSize = 5;
        bool postProcess = true;
    };

    using TranscriptionCallback = std::function<void(const std::string &, bool isFinal)>;

    SpeechRecognizer();
    ~SpeechRecognizer();

    bool Initialize(const Config &config);
    void Shutdown();

    void SetCallback(TranscriptionCallback callback) { callback_ = callback; }

    // Process audio data
    void ProcessAudio(const std::vector<float> &audioData);

    // Model management
    bool LoadModel(const std::string &modelPath);
    std::string GetModelPath(Model model) const;

    // Configuration
    void SetLanguage(const std::string &language);
    void SetBeamSize(int beamSize);
    void SetThreads(int threads);

    // Reset whisper context state for fresh processing
    void ResetState();

    // Get current model type
    Model GetModelType() const { return config_.model; }

private:
    void ProcessingThread();

    struct whisper_context *ctx_;
    Config config_;
    TranscriptionCallback callback_;

    std::thread processingThread_;
    std::queue<std::vector<float>> audioQueue_;
    std::mutex queueMutex_;
    std::condition_variable queueCV_;
    std::atomic<bool> shouldStop_;

    // Audio buffer for continuous processing
    std::vector<float> audioBuffer_;
    std::mutex bufferMutex_;
};